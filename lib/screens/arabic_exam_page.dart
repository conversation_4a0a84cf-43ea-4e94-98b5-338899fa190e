import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:intp_iraq/services/question_service.dart';
import 'package:intp_iraq/services/subscription_service.dart';
import 'package:intp_iraq/screens/premium_subscription_page.dart';
import 'package:intp_iraq/screens/home_page.dart'; // إضافة استيراد صفحة الرئيسية
import 'dart:async';

class ArabicExamPage extends StatefulWidget {
  const ArabicExamPage({super.key});

  @override
  _ArabicExamPageState createState() => _ArabicExamPageState();
}

class _ArabicExamPageState extends State<ArabicExamPage> {
  bool _mounted = true;
  int _currentQuestionIndex = 0;
  int _score = 0;
  bool _showResult = false;
  List<Map<String, dynamic>> _questions = [];
  bool _isLoading = true;
  bool _error = false;
  int _remainingTime = 3600; // ساعة واحدة بالثواني
  Timer? _timer;
  List<int?> _userAnswers = [];

  @override
  void initState() {
    super.initState();
    _loadQuestions();
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_mounted) {
        setState(() {
          if (_remainingTime > 0) {
            _remainingTime--;
          } else {
            _timer?.cancel();
            _calculateScore();
            _showResult = true;
          }
        });
      } else {
        _timer?.cancel();
      }
    });
  }

  @override
  void dispose() {
    _mounted = false;
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _loadQuestions() async {
    if (!_mounted) return;
    setState(() {
      _isLoading = true;
      _error = false;
    });

    try {
      List<Map<String, dynamic>> allQuestions =
          await QuestionService.getQuestions('arabic');
      if (_mounted) {
        setState(() {
          allQuestions.shuffle();
          _questions = allQuestions.take(40).toList();
          _userAnswers = List.filled(_questions.length, null);
          _isLoading = false;
          _remainingTime = 3600;
          _startTimer();
        });
      }
    } catch (e) {
      print('Error loading questions: $e');
      if (_mounted) {
        setState(() {
          _isLoading = false;
          _error = true;
        });
      }
    }
  }

  void _answerQuestion(int selectedAnswer) {
    if (_mounted) {
      setState(() {
        _userAnswers[_currentQuestionIndex] = selectedAnswer;
      });
    }
  }

  void _goToNextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1 && _mounted) {
      setState(() => _currentQuestionIndex++);
    }
  }

  void _goToPreviousQuestion() {
    if (_currentQuestionIndex > 0 && _mounted) {
      setState(() => _currentQuestionIndex--);
    }
  }

  void _calculateScore() {
    _score = 0;
    for (int i = 0; i < _questions.length; i++) {
      if (_userAnswers[i] == _questions[i]['correctAnswer']) {
        _score++;
      }
    }
  }

  Future<void> _resetQuiz() async {
    String examType = 'arabic'; // تحديد نوع الامتحان
    
    try {
      bool isPremium = await SubscriptionService.isPremiumUser();
      if (!isPremium) {
        bool canTake = await SubscriptionService.canTakeExam(examType);
        if (!canTake) {
          if (!mounted) return;
          // تعديل ليفتح صفحة الاشتراك المدفوع مع تمرير معلومة أنه قادم من صفحة نتائج الامتحان
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PremiumSubscriptionPage(
                onReturn: () {
                  // عند الرجوع، سيتم توجيه المستخدم إلى الصفحة الرئيسية
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(builder: (context) => HomePage()),
                  );
                }
              ),
            )
          );
          return;
        }
        await SubscriptionService.recordExamAttempt(examType);
      }

      if (mounted) {
        setState(() {
          _currentQuestionIndex = 0;
          _score = 0;
          _showResult = false;
          _questions.shuffle();
          _userAnswers = List.filled(_questions.length, null);
          _remainingTime = 3600;
        });
        _startTimer();
      }
    } catch (e) {
      print('Error checking exam permission: $e');
      if (!mounted) return;
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PremiumSubscriptionPage(
            onReturn: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => HomePage()),
              );
            }
          ),
        )
      );
    }
  }

  double get _finalScore => (_score / _questions.length) * 100;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          // Background
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.blue.shade900, Colors.blue.shade200],
              ),
            ),
          ),
          // Content
          SafeArea(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Colors.white))
                : _error
                    ? _buildErrorWidget()
                    : _showResult
                        ? _buildResultScreen()
                        : _buildQuestionScreen(),
          ),
        ],
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      leading: IconButton(
        icon: const Icon(CupertinoIcons.back, color: Colors.white),
        onPressed: () {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text(
                  'تأكيد الخروج',
                  textAlign: TextAlign.right,
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                content: Text(
                  'هل أنت متأكد أنك تريد الخروج من الامتحان؟\nسيتم فقدان جميع إجاباتك الحالية.',
                  textAlign: TextAlign.right,
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
                actions: <Widget>[
                  TextButton(
                    child: Text('البقاء في الامتحان',
                        style: TextStyle(fontFamily: 'Cairo')),
                    onPressed: () {
                      Navigator.of(context).pop(); // إغلاق مربع الحوار
                    },
                  ),
                  TextButton(
                    child: Text('الخروج',
                        style:
                            TextStyle(fontFamily: 'Cairo', color: Colors.red)),
                    onPressed: () {
                      Navigator.of(context).pop(); // إغلاق مربع الحوار
                      Navigator.of(context).pop(); // الخروج من صفحة الامتحان
                    },
                  ),
                ],
              );
            },
          );
        },
      ),
      title: Text(
        'امتحان اللغة العربية',
        style: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 24,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: Colors.blue.shade900,
      elevation: 0,
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'حدث خطأ أثناء تحميل الأسئلة',
            style: TextStyle(color: Colors.white, fontSize: 18),
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: _loadQuestions,
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.blue.shade900,
              backgroundColor: Colors.white,
            ),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionScreen() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final double screenHeight = constraints.maxHeight;
        final double screenWidth = constraints.maxWidth;
        final bool isSmallScreen = screenWidth < 600;

        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(screenWidth * 0.05),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildTimerWidget(),
                LinearProgressIndicator(
                  value: (_currentQuestionIndex + 1) / _questions.length,
                  backgroundColor: Colors.blue.shade100,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
                SizedBox(height: screenHeight * 0.02),
                Text(
                  'السؤال ${_currentQuestionIndex + 1} من ${_questions.length}',
                  style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: isSmallScreen ? 16 : 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenHeight * 0.03),
                _buildQuestionCard(screenHeight),
                SizedBox(height: screenHeight * 0.03),
                ..._buildAnswerButtons(screenWidth, isSmallScreen),
                SizedBox(height: screenHeight * 0.02),
                _buildNavigationButtons(screenWidth),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTimerWidget() {
    return Text(
      'الوقت المتبقي: ${(_remainingTime ~/ 60).toString().padLeft(2, '0')}:${(_remainingTime % 60).toString().padLeft(2, '0')}',
      style: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildQuestionCard(double screenHeight) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      color: Colors.white.withOpacity(0.9),
      child: Padding(
        padding: EdgeInsets.all(screenHeight * 0.02),
        child: Text(
          _questions[_currentQuestionIndex]['question'],
          style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade900),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  List<Widget> _buildAnswerButtons(double screenWidth, bool isSmallScreen) {
    return (_questions[_currentQuestionIndex]['answers'] as List<dynamic>)
        .asMap()
        .entries
        .map((entry) {
      bool isSelected = _userAnswers[_currentQuestionIndex] == entry.key;
      return Padding(
        padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 4.0 : 8.0),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: isSelected ? Colors.white : Colors.blue.shade900,
            backgroundColor: isSelected ? Colors.blue.shade700 : Colors.white,
            padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 10 : 15),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            elevation: 5,
          ),
          onPressed: () => _answerQuestion(entry.key),
          child: Text(
            entry.value,
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: isSmallScreen ? 15 : 18,
              fontWeight: FontWeight.bold, // إضافة هذا السطر
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildNavigationButtons(double screenWidth) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ElevatedButton(
          onPressed: _currentQuestionIndex < _questions.length - 1
              ? _goToNextQuestion
              : () {
                  _calculateScore();
                  if (_mounted) {
                    setState(() => _showResult = true);
                  }
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade700,
            foregroundColor: Colors.white,
          ),
          child: Text(
              _currentQuestionIndex < _questions.length - 1
                  ? 'السؤال التالي'
                  : 'إنهاء الامتحان',
              style: TextStyle(fontFamily: 'Cairo', fontSize: 15)),
        ),
        ElevatedButton(
          onPressed: _currentQuestionIndex > 0 ? _goToPreviousQuestion : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade700,
            foregroundColor: Colors.white,
          ),
          child: Text('السؤال السابق',
              style: TextStyle(fontFamily: 'Cairo', fontSize: 15)),
        ),
      ],
    );
  }

  Widget _buildResultScreen() {
    return Center(
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        color: Colors.white.withOpacity(0.9),
        child: Padding(
          padding: const EdgeInsets.all(25.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'نتيجة الامتحان',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade900,
                ),
              ),
              SizedBox(height: 15),
              Text(
                'لقد أجبت على $_score من أصل ${_questions.length} سؤال بشكل صحيح',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 16,
                  color: Colors.blue.shade800,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 15),
              Text(
                'النتيجة النهائية',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade900,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'لقد حصلت على ${_finalScore.toStringAsFixed(1)} من 100',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
              SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _resetQuiz,
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Colors.blue.shade700,
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        'إعادة الامتحان',
                        style: TextStyle(fontFamily: 'Cairo', fontSize: 16),
                      ),
                    ),
                  ),
                  SizedBox(width: 10),
                  Container(
                    width: 140,
                    child: ElevatedButton(
                      onPressed: () => _showWrongAnswers(),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Colors.red.shade700,
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        'الإجابات الخاطئة',
                        style: TextStyle(fontFamily: 'Cairo', fontSize: 14),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showWrongAnswers() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'الإجابات الخاطئة',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade700,
                      ),
                    ),
                    SizedBox(height: 20),
                    ..._getWrongAnswersWidgets(),
                    SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade700,
                      ),
                      child: Text(
                        'إغلاق',
                        style: TextStyle(fontFamily: 'Cairo', fontSize: 16 , color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _getWrongAnswersWidgets() {
    List<Widget> widgets = [];
    for (int i = 0; i < _questions.length; i++) {
      if (_userAnswers[i] != _questions[i]['correctAnswer']) {
        widgets.add(Card(
          elevation: 4,
          margin: EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'السؤال: ${_questions[i]['question']}',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.right,
                ),
                SizedBox(height: 10),
                if (_userAnswers[i] != null) Text(
                  'إجابتك: ${_questions[i]['answers'][_userAnswers[i]]}',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.right,
                ),
                SizedBox(height: 5),
                Text(
                  'الإجابة الصحيحة: ${_questions[i]['answers'][_questions[i]['correctAnswer']]}',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14,
                    color: Colors.green,
                  ),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
        ));
      }
    }
    return widgets;
  }
}
