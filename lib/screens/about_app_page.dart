import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutAppPage extends StatelessWidget {
  const AboutAppPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl, // تعيين اتجاه النص من اليمين لليسار
      child: Scaffold(
        backgroundColor: const Color(0xFFFAFAFA), // لون خلفية الصفحة
        appBar: AppBar(
          backgroundColor: const Color(0xFFFAFAFA), // تعيين نفس لون الخلفية للأب بار
          title: Text('حول التطبيق', style: TextStyle(fontFamily: 'Cairo')),
          centerTitle: true,
          elevation: 0, // إزالة الظل للحصول على مظهر أكثر سلاسة
          automaticallyImplyLeading: false, // إزالة زر الرجوع التلقائي
          actions: [
            IconButton(
              icon: Icon(Icons.arrow_forward_ios), // استخدام أيقونة سهم iOS للأمام
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
        body: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildTitle(constraints),
                      SizedBox(height: constraints.maxHeight * 0.02),
                      _buildInfoCard(
                        'نبذة عن التطبيق',
                        'تطبيق "مساعد اختبار الكفاءة" هو أداة تعليمية مصممة للمساعدة على تعزيز المهارات والاستعداد في مجالات متنوعة. حيث يوفر التطبيق مجموعة واسعة من الأسئلة التفاعلية في مجالات متعددة مثل اللغة العربية، اللغة الإنجليزية، والحاسوب، للمساعدة على تحسين المستوى وزيادة الثقافه العامه.',
                        icon: Icons.info_outline,
                        constraints: constraints,
                      ),
                      SizedBox(height: constraints.maxHeight * 0.01),
                      _buildInfoCard(
                        'إخلاء المسؤولية',
                        'هذا التطبيق هوا تطبيق غير رسمي وغير تابع لاي جهة حكومية. جميع المحتويات والأسئلة المقدمة داخل التطبيق هي لأغراض تعليمية فقط، وتم جمعها من مصادر تعليمية مفتوحة وغير رسمية.',
                        icon: Icons.warning_amber_rounded,
                        color: Colors.orange[50],
                        textColor: Colors.orange[800],
                        constraints: constraints,
                      ),
                      SizedBox(height: constraints.maxHeight * 0.01),
                      _buildPrivacyPolicyCard(context, constraints),
                      SizedBox(height: constraints.maxHeight * 0.01),
                      _buildInfoCard(
                        'طريقة حذف الحساب',
                        '1. انتقل إلى قائمة الإعدادات داخل التطبيق.\n2. مرِّر للأسفل حتى تجد خيار "حذف الحساب".\n3. اضغط على "حذف الحساب".\n4. أكد رغبتك في الحذف.\n\nتنبيه: بعد الحذف، سيتم مسح جميع بياناتك بشكل دائم ولا يمكن استرجاعها.',
                        icon: Icons.delete_outline,
                        constraints: constraints,
                      ),
                      SizedBox(height: constraints.maxHeight * 0.01),
                      _buildInfoCard(
                        'للتواصل والدعم',
                        'إذا كانت لديك أي استفسارات أو واجهت مشكلات، يُرجى التواصل معنا عبر البريد الإلكتروني:\n📧 <EMAIL>',
                        icon: Icons.support_agent,
                        constraints: constraints,
                      ),
                      SizedBox(height: constraints.maxHeight * 0.01),
                      _buildInfoCard(
                        'إصدار التطبيق',
                        '1.1.15',
                        icon: Icons.new_releases,
                        constraints: constraints,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BoxConstraints constraints) {
    return Text(
      'تطبيق مساعد اختبار الكفاءة',
      style: TextStyle(
        fontSize: constraints.maxWidth * 0.05,
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
        color: Colors.blue.shade800,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildInfoCard(String title, String text, {Color? color, Color? textColor, IconData? icon, required BoxConstraints constraints}) {
    return Card(
      elevation: 2,
      color: color ?? Colors.blue.shade50,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {}, // إضافة تأثير عند الضغط
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(constraints.maxWidth * 0.03),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (icon != null)
                Icon(icon, color: textColor ?? Colors.blue.shade800, size: constraints.maxWidth * 0.06),
              if (icon != null)
                SizedBox(height: constraints.maxHeight * 0.005),
              Text(
                title,
                style: TextStyle(
                  fontSize: constraints.maxWidth * 0.04,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                  color: textColor ?? Colors.blue.shade800,
                ),
              ),
              SizedBox(height: constraints.maxHeight * 0.005),
              Text(
                text,
                style: TextStyle(
                  fontSize: constraints.maxWidth * 0.035,
                  fontFamily: 'Cairo',
                  color: textColor ?? Colors.blue.shade800,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrivacyPolicyCard(BuildContext context, BoxConstraints constraints) {
    return Card(
      elevation: 2,
      color: Colors.blue.shade50,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {}, // إضافة تأثير عند الضغط
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(constraints.maxWidth * 0.03),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(Icons.privacy_tip_outlined, color: Colors.blue.shade800, size: constraints.maxWidth * 0.06),
              SizedBox(height: constraints.maxHeight * 0.005),
              Text(
                'سياسة الخصوصية',
                style: TextStyle(
                  fontSize: constraints.maxWidth * 0.04,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                  color: Colors.blue.shade800,
                ),
              ),
              SizedBox(height: constraints.maxHeight * 0.005),
              Text(
                'يمكنك الاطلاع على سياسة الخصوصية الخاصة بنا لمعرفة كيفية تعاملنا مع بياناتك.',
                style: TextStyle(
                  fontSize: constraints.maxWidth * 0.035,
                  fontFamily: 'Cairo',
                  color: Colors.blue.shade800,
                ),
              ),
              SizedBox(height: constraints.maxHeight * 0.005),
              ElevatedButton(
                onPressed: () => _launchURL(context, 'https://tqsolutions-kifaa.netlify.app'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade800,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('عرض سياسة الخصوصية', style: TextStyle(fontFamily: 'Cairo',color: Colors.white)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _launchURL(BuildContext context, String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('لا يمكن فتح الرابط: $urlString')),
        );
      }
    }
  }
}