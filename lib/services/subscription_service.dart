import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class SubscriptionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  static Future<bool> isPremiumUser() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      // التحقق من طريقة المصادقة للمستخدم
      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      Map<String, dynamic>? userData;

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();

        if (userDocs.docs.isNotEmpty) {
          userData = userDocs.docs.first.data();
        }
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        userData = userDoc.data();
      }

      if (userData == null) return false;

      // التحقق من حالة الاشتراك المميز
      bool isPremium = userData['isPremium'] ?? false;
      if (!isPremium) return false;

      // التحقق من تاريخ انتهاء الاشتراك
      if (userData['premiumExpiryDate'] != null) {
        Timestamp expiryTimestamp = userData['premiumExpiryDate'];
        DateTime expiryDate = expiryTimestamp.toDate();

        // إذا انتهت صلاحية الاشتراك، قم بتحديث الحالة
        if (DateTime.now().isAfter(expiryDate)) {
          await _updateExpiredSubscription(user, isGoogleSignIn);
          return false;
        }
      }

      return true;
    } catch (e) {
      print('Error checking premium status: $e');
      return false;
    }
  }

  static Future<bool> canTakeExam(String examType) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      final isPremium = await isPremiumUser();
      if (isPremium) return true;

      // التحقق من طريقة المصادقة للمستخدم
      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();
            
        if (userDocs.docs.isNotEmpty) {
          final userData = userDocs.docs.first.data();
          final attempts = userData['examAttempts'] ?? {};
          final hasAttempted = attempts.containsKey(examType) && attempts[examType] == true;
          return !hasAttempted;
        }
        return true; // لم يتم العثور على المستخدم، نسمح بالمحاولة الأولى
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        final userData = userDoc.data();
        final attempts = userData?['examAttempts'] ?? {};
        final hasAttempted = attempts.containsKey(examType) && attempts[examType] == true;
        return !hasAttempted;
      }
    } catch (e) {
      print('Error checking exam attempt: $e');
      return false;
    }
  }

  static Future<void> recordExamAttempt(String examType) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final isPremium = await isPremiumUser();
      if (isPremium) return; // لا نسجل المحاولات للمستخدمين المميزين

      // التحقق من طريقة المصادقة للمستخدم
      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();
            
        if (userDocs.docs.isNotEmpty) {
          final docId = userDocs.docs.first.id;
          Map<String, dynamic> currentAttempts = (userDocs.docs.first.data()['examAttempts']) ?? {};
          currentAttempts[examType] = true;

          await _firestore.collection('users').doc(docId).update({
            'examAttempts': currentAttempts,
          });
        }
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        Map<String, dynamic> currentAttempts = (userDoc.data()?['examAttempts']) ?? {};
        currentAttempts[examType] = true;

        await _firestore.collection('users').doc(user.uid).update({
          'examAttempts': currentAttempts,
        });
      }
    } catch (e) {
      print('Error recording exam attempt: $e');
    }
  }

  static Future<void> upgradeToPremium(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).set({
        'isPremium': true,
        'premiumStartDate': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error upgrading to premium: $e');
      throw e;
    }
  }
  
  static Future<void> removePremium(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).set({
        'isPremium': false,
        'premiumExpiryDate': null,
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error removing premium status: $e');
      throw e;
    }
  }

  // تحديث الاشتراك المنتهي الصلاحية
  static Future<void> _updateExpiredSubscription(User user, bool isGoogleSignIn) async {
    try {
      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();

        if (userDocs.docs.isNotEmpty) {
          final docId = userDocs.docs.first.id;
          await _firestore.collection('users').doc(docId).update({
            'isPremium': false,
            'premiumExpiryDate': null,
          });
        }
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        await _firestore.collection('users').doc(user.uid).update({
          'isPremium': false,
          'premiumExpiryDate': null,
        });
      }
    } catch (e) {
      print('Error updating expired subscription: $e');
    }
  }

  // الحصول على تاريخ انتهاء الاشتراك
  static Future<DateTime?> getPremiumExpiryDate() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return null;

      // التحقق من طريقة المصادقة للمستخدم
      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      Map<String, dynamic>? userData;

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();

        if (userDocs.docs.isNotEmpty) {
          userData = userDocs.docs.first.data();
        }
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        userData = userDoc.data();
      }

      if (userData == null || userData['premiumExpiryDate'] == null) {
        return null;
      }

      Timestamp expiryTimestamp = userData['premiumExpiryDate'];
      return expiryTimestamp.toDate();
    } catch (e) {
      print('Error getting premium expiry date: $e');
      return null;
    }
  }
}